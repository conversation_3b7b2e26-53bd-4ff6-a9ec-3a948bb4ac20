"""
TTS引擎核心模块，负责与各种语音合成引擎交互
"""
import os
import asyncio
import edge_tts
import aiohttp
import subprocess
import tempfile
import shutil
import json
import re
import pysrt  
import numpy as np



class TTSEngine:
    """TTS引擎类，处理文本到语音的转换"""
    
    def __init__(self, settings):
        """
        初始化TTS引擎
        
        参数:
            settings: 配音设置字典
        """
        self.settings = settings
        self.temp_files = []
        self.stop_flag = False
        # 新增：批量任务列表
        self._batch_tasks = []  # 保存所有批量处理的asyncio.Task对象
        # 创建事件循环
        self.loop = asyncio.new_event_loop()
        
        # 初始化子进程信息
        self.startupinfo = None
        if os.name == 'nt':  # 如果是Windows系统
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            self.startupinfo.wShowWindow = subprocess.SW_HIDE
            # 设置进程创建标志，完全分离控制台
            self.creation_flags = subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
        else:
            self.creation_flags = 0
    
    def _preprocess_text(self, text):
        """
        特殊读法预处理，严格按需求处理：
        1. 区间/带单位的-（如 10-20、50公斤-60公斤）读作"至"
        2. 数字前的-（如 -10）读作"减"
        3. 非数字/非ASCII后接-数字（如 伤害-10）读作"减"
        4. 其他所有-都读作"至"
        其他符号（+、/、*、%）按原逻辑处理
        """
        # 先处理百分号：数字% -> 百分之数字
        text = re.sub(r'(\d+)%', r'百分之\1', text)
        # 处理加号
        text = text.replace('+', '加')
        # 处理除号
        text = text.replace('/', '比')
        # 处理乘号
        text = text.replace('*', '乘')
        # 1. 区间/带单位的-（如 10-20、50公斤-60公斤）
        text = re.sub(r'(\d+)\s*(?:[公斤克千米吨厘寸尺毫微个份包片元角分度]*)-((?:\s*)[0-9]+)', r'\1至\2', text)
        # 2. 数字前的-（如 -10）
        text = re.sub(r'(^|\s+)-(\d)', r'\1减\2', text)
        # 3. 非数字/非ASCII后接-数字（如 伤害-10）
        text = re.sub(r'([^\u0000-\u007F\d])-(\d)', r'\1减\2', text)
        # 4. 其他所有-都读作"至"
        text = text.replace('-', '至')
        return text

    def _subtitle_to_seconds(self, time_obj):
        """
        将字幕时间对象转换为秒数

        参数:
            time_obj: pysrt时间对象

        返回:
            float: 时间（秒）
        """
        return time_obj.hours * 3600 + time_obj.minutes * 60 + time_obj.seconds + time_obj.milliseconds / 1000.0

    def _set_subtitle_time(self, time_obj, seconds):
        """
        将秒数设置到字幕时间对象

        参数:
            time_obj: pysrt时间对象
            seconds: 时间（秒）
        """
        time_obj.hours = int(seconds // 3600)
        time_obj.minutes = int((seconds % 3600) // 60)
        time_obj.seconds = int(seconds % 60)
        time_obj.milliseconds = int((seconds - int(seconds)) * 1000)

    def generate_preview(self, text, output_file):
        """
        生成预览音频文件
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 预处理文本用于语音合成，字幕保留原文
            tts_text = self._preprocess_text(text)
            # 运行异步代码
            self.loop.run_until_complete(self._generate_preview_async(tts_text, output_file))
        except Exception as e:
            print(f"生成预览失败: {str(e)}")
            raise e
    
    async def _generate_preview_async(self, text, output_file):
        """
        异步生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 仅支持 Edge TTS，引擎字段若非 Edge 自动回退
        engine = self.settings.get("engine", "edge-tts")
        if engine != "edge-tts":
            engine = "edge-tts"
            self.settings["engine"] = "edge-tts"
        
        await self._edge_tts_preview(text, output_file)
        # 创建字幕文件
        raw_txt_file = os.path.splitext(output_file)[0] + ".raw.txt"
        with open(raw_txt_file, 'w', encoding='utf-8') as f:
            f.write(text + '\n')
        srt_file = os.path.splitext(output_file)[0] + ".srt"
    
    async def _edge_tts_preview(self, text, output_file):
        """
        使用Edge TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 从设置中获取参数
            voice = self.settings["voice"]
            # 修复计算逻辑，确保rate和volume使用正确的格式
            speed = self.settings['speed']
            rate = f"+{int((speed - 1) * 100)}%" if speed > 1 else f"-{int(abs((speed - 1) * 100))}%"
            if speed == 1:
                rate = "+0%"
            volume = f"+{int((self.settings['volume'] - 1) * 100)}%" if self.settings['volume'] > 1 else f"-{int(abs((self.settings['volume'] - 1) * 100))}%"
            if self.settings['volume'] == 1:
                volume = "+0%"
            
            # 修复pitch参数格式，使用Hz单位
            pitch_value = int((self.settings['pitch'] - 1) * 50)  # 将0.5-2.0的值映射到-25Hz到+50Hz
            pitch = f"+{pitch_value}Hz" if pitch_value >= 0 else f"{pitch_value}Hz"
            if self.settings['pitch'] == 1:
                pitch = "+0Hz"
            
            # 使用Edge TTS生成音频
            communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume, pitch=pitch)
            await communicate.save(output_file)
        except Exception as e:
            print(f"Edge TTS预览失败: {str(e)}")
            raise e
    
    async def _azure_tts_preview(self, text, output_file):
        """
        使用Azure TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API密钥是否已设置
            api_key = self.settings.get("azure_key", "")
            region = self.settings.get("azure_region", "")
            
            if not api_key or not region:
                raise Exception("Azure TTS API密钥或区域未设置，请在设置中配置")
            
            # 从设置中获取参数
            voice = self.settings["voice"]
            speed = self.settings['speed']
            pitch = self.settings['pitch']
            volume = self.settings['volume']
            
            # 构建SSML
            ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
                <voice name="{voice}">
                    <prosody rate="{speed}" pitch="{int((pitch-1)*50)}Hz" volume="{int((volume-1)*100)}%">
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
            
            # 设置API请求头
            headers = {
                "Ocp-Apim-Subscription-Key": api_key,
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3",
                "User-Agent": "TTS-Tool"
            }
            
            # 设置API请求URL
            url = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"
            
            # 发送请求并获取音频数据
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=ssml.encode('utf-8')) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Azure TTS API错误: {response.status}, {error_text}")
                    
                    # 将音频数据写入文件
                    with open(output_file, "wb") as f:
                        f.write(await response.read())
            
        except Exception as e:
            print(f"Azure TTS预览失败: {str(e)}")
            raise e
    
    async def _baidu_tts_preview(self, text, output_file):
        """
        使用百度TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            app_id = self.settings.get("baidu_app_id", "")
            api_key = self.settings.get("baidu_api_key", "")
            secret_key = self.settings.get("baidu_secret_key", "")
            
            if not app_id or not api_key or not secret_key:
                raise Exception("百度TTS API信息未设置，请在设置中配置")
            
            # 从设置中获取参数
            speed = int(self.settings['speed'] * 5)  # 百度TTS语速范围0-9
            pitch = int(self.settings['pitch'] * 9)  # 百度TTS音调范围0-9
            volume = int(self.settings['volume'] * 15)  # 百度TTS音量范围0-15
            
            # 获取访问令牌的URL
            token_url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={api_key}&client_secret={secret_key}"
            
            # 获取访问令牌
            async with aiohttp.ClientSession() as session:
                async with session.post(token_url) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"获取百度访问令牌失败: {response.status}, {error_text}")
                    
                    result = await response.json()
                    access_token = result.get("access_token")
                    
                    if not access_token:
                        raise Exception("获取百度访问令牌失败")
                    
                    # 构建TTS API请求参数
                    tts_url = "https://tsn.baidu.com/text2audio"
                    params = {
                        "tex": text,
                        "tok": access_token,
                        "cuid": app_id,
                        "ctp": 1,
                        "lan": "zh",
                        "spd": speed,
                        "pit": pitch,
                        "vol": volume,
                        "per": 0,  # 发音人，可根据需要修改
                        "aue": 3,  # mp3格式
                    }
                    
                    # 发送TTS请求
                    async with session.post(tts_url, data=params) as tts_response:
                        content_type = tts_response.headers.get('Content-Type')
                        
                        if content_type == 'application/json':
                            # 错误响应
                            error_json = await tts_response.json()
                            raise Exception(f"百度TTS合成失败: {error_json}")
                        
                        # 将音频数据写入文件
                        with open(output_file, "wb") as f:
                            f.write(await tts_response.read())
            
        except Exception as e:
            print(f"百度TTS预览失败: {str(e)}")
            raise e
    
    async def _xunfei_tts_preview(self, text, output_file):
        """
        使用讯飞TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            app_id = self.settings.get("xunfei_app_id", "")
            api_key = self.settings.get("xunfei_api_key", "")
            api_secret = self.settings.get("xunfei_api_secret", "")
            
            if not app_id or not api_key or not api_secret:
                raise Exception("讯飞TTS API信息未设置，请在设置中配置")
            
            # 由于讯飞TTS API的复杂性，这里只提供示例代码
            # 讯飞TTS需要使用WebSocket进行通信，需要更复杂的实现
            # 这里使用临时方案，通过subprocess调用外部脚本或使用SDK
            
            # 创建临时目录存放配置文件
            temp_dir = tempfile.mkdtemp()
            config_file = os.path.join(temp_dir, "xunfei_config.json")
            
            # 创建配置文件
            config = {
                "app_id": app_id,
                "api_key": api_key,
                "api_secret": api_secret,
                "text": text,
                "speed": self.settings['speed'],
                "pitch": self.settings['pitch'],
                "volume": self.settings['volume']
            }
            
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False)
            
            # 这里需要一个外部脚本或SDK来实际调用讯飞TTS API
            # 这里只是示例，实际应用需要根据讯飞TTS API的文档进行实现
            raise Exception("讯飞TTS功能尚未完全实现，请等待后续更新")
            
        except Exception as e:
            print(f"讯飞TTS预览失败: {str(e)}")
            raise e
    
    async def _tencent_tts_preview(self, text, output_file):
        """
        使用腾讯TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            secret_id = self.settings.get("tencent_secret_id", "")
            secret_key = self.settings.get("tencent_secret_key", "")
            
            if not secret_id or not secret_key:
                raise Exception("腾讯TTS API信息未设置，请在设置中配置")
            
            # 由于腾讯TTS API的复杂性，这里只提供示例代码
            # 腾讯TTS需要使用签名等安全机制，需要更复杂的实现
            # 这里使用临时方案，通过subprocess调用外部脚本或使用SDK
            
            # 创建临时目录存放配置文件
            temp_dir = tempfile.mkdtemp()
            config_file = os.path.join(temp_dir, "tencent_config.json")
            
            # 创建配置文件
            config = {
                "secret_id": secret_id,
                "secret_key": secret_key,
                "text": text,
                "speed": self.settings['speed'],
                "pitch": self.settings['pitch'],
                "volume": self.settings['volume']
            }
            
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False)
            
            # 这里需要一个外部脚本或SDK来实际调用腾讯TTS API
            # 这里只是示例，实际应用需要根据腾讯TTS API的文档进行实现
            raise Exception("腾讯TTS功能尚未完全实现，请等待后续更新")
            
        except Exception as e:
            print(f"腾讯TTS预览失败: {str(e)}")
            raise e
    
    def generate_audio(self, text, output_file, progress_callback=None):
        """
        生成完整音频文件
        参数:
            text: 文本内容
            output_file: 输出文件路径
            progress_callback: 进度回调函数，接收参数(进度百分比, 状态文本)
        """
        try:
            # 重置停止标志
            self.stop_flag = False
            # 预处理文本用于语音合成，字幕保留原文
            tts_text = self._preprocess_text(text)
            # 运行异步代码
            self.loop.run_until_complete(
                self._generate_audio_async(tts_text, output_file, progress_callback)
            )
        except Exception as e:
            print(f"生成音频失败: {str(e)}")
            # 清理临时文件
            self._cleanup_temp_files()
            raise e
    
    async def _generate_audio_async(self, text, output_file, progress_callback=None):
        """
        异步生成完整音频
        """
        try:
            # 准备输出目录
            output_dir = os.path.dirname(output_file)
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建临时目录
            temp_dir = os.path.join(output_dir, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成字幕文件路径
            srt_file = os.path.splitext(output_file)[0] + ".srt"
            # 新增：保存原始文本分行到 .raw.txt
            raw_txt_file = os.path.splitext(output_file)[0] + ".raw.txt"
            
            # 保存原始文本，包含行分隔
            original_lines = text.splitlines()
            with open(raw_txt_file, 'w', encoding='utf-8') as f:
                for line in original_lines:
                    f.write(line + '\n')
            
            # 更新进度
            if progress_callback:
                progress_callback(5, "分析文本...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            
            # 按行分割文本
            lines = text.splitlines()
            
            # 保存原始行与行号的映射，用于后续字幕匹配
            self.original_text_mapping = {i: line for i, line in enumerate(lines) if line.strip()}
            
            # 计算批次大小
            batch_size = self.settings["batch_size"]
            line_batches = []
            current_batch = []
            current_size = 0
            
            # 保存每个批次中包含的原始行索引，用于后续字幕匹配
            batch_line_indices = []
            current_indices = []
            
            for i, line in enumerate(lines):
                line_len = len(line)
                if current_size + line_len > batch_size and current_batch:
                    line_batches.append("\n".join(current_batch))
                    batch_line_indices.append(current_indices)
                    current_batch = [line]
                    current_indices = [i]
                    current_size = line_len
                else:
                    current_batch.append(line)
                    current_indices.append(i)
                    current_size += line_len
                    
            # 添加最后一个批次
            if current_batch:
                line_batches.append("\n".join(current_batch))
                batch_line_indices.append(current_indices)
            
            # 更新进度
            if progress_callback:
                progress_callback(10, f"准备处理 {len(line_batches)} 个批次...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            
            # 生成每个批次的临时文件
            temp_audio_files = []
            temp_srt_files = []
            # 设置并发数
            concurrent_tasks = min(self.settings.get("concurrent_tasks", 4), len(line_batches))
            semaphore = asyncio.Semaphore(concurrent_tasks)
            print(f"使用并行任务数：{concurrent_tasks}，批次总数：{len(line_batches)}")
            tasks = []
            self._batch_tasks = []  # 重置批量任务列表
            # 初始化已完成批次数和当前进度
            self._completed_batches = 0
            self._current_progress = 0  # 0-10% 用于批次开始动态显示
            for i, batch_text in enumerate(line_batches):
                temp_file = os.path.join(temp_dir, f"batch_{i+1}.mp3")
                temp_srt = os.path.join(temp_dir, f"batch_{i+1}.srt")
                temp_audio_files.append(temp_file)
                temp_srt_files.append(temp_srt)
                self.temp_files.append(temp_srt)  # 确保批次字幕也加入临时文件列表
                # 用asyncio.create_task创建任务，便于后续取消
                task = asyncio.create_task(self._process_batch(
                    i,
                    batch_text,
                    temp_file,
                    temp_srt,
                    semaphore,
                    len(line_batches),
                    progress_callback,
                    batch_line_indices[i]  # 传递当前批次的原始行索引
                ))
                tasks.append(task)
                self._batch_tasks.append(task)
            # 检查是否被停止
            if self.stop_flag:
                for task in self._batch_tasks:
                    if not task.done():
                        task.cancel()
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            # 并发执行所有批次
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
            except asyncio.CancelledError:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            # 检查是否有任务失败
            failed_tasks = [i for i, result in enumerate(results) if isinstance(result, Exception)]
            if failed_tasks:
                failed_indices = ", ".join([str(i+1) for i in failed_tasks])
                raise Exception(f"以下批次处理失败: {failed_indices}")
            
            # 更新进度
            if progress_callback:
                progress_callback(80, "合并音频...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            
            # 合并所有临时文件
            await self._merge_audio_files(output_file, temp_audio_files)
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            
            # 更新进度
            if progress_callback:
                progress_callback(90, "合并字幕...")
            
            # 合并字幕文件
            await self._merge_subtitle_files(srt_file, temp_srt_files, temp_audio_files, progress_callback)
            
            # 更新进度
            if progress_callback:
                progress_callback(100, "处理完成")
            
        except asyncio.CancelledError:
            if progress_callback:
                progress_callback(0, "操作已取消")
            return
        except Exception as e:
            print(f"生成音频失败: {str(e)}")
            raise e
        finally:
            # 清理未完成的临时文件（保留已完成的）
            self._cleanup_temp_files()
            self._batch_tasks = []  # 清空任务列表
    
    async def _merge_subtitle_files(self, output_srt, srt_files, audio_files, progress_callback=None):
        """
        简化的字幕合并方法，按SRT时间顺序累加偏移

        参数:
            output_srt: 输出字幕文件路径
            srt_files: 要合并的字幕文件列表
            audio_files: 对应的音频文件列表（用于获取长度信息）
        """
        try:
            if not srt_files:
                return

            print("开始字幕合并流程...")

            # 创建最终字幕文件
            final_subs = pysrt.SubRipFile()
            subtitle_index = 1

            # 累积时间偏移（秒）
            cumulative_time_offset = 0.0

            # 获取所有音频文件的时长
            print("获取音频文件时长...")
            audio_durations = []

            for i, audio_file in enumerate(audio_files):
                if not os.path.exists(audio_file):
                    print(f"音频文件不存在: {audio_file}")
                    audio_durations.append(0.0)
                    continue

                try:
                    # 使用FFprobe获取音频时长
                    command = [
                        "ffprobe",
                        "-v", "error",
                        "-show_entries", "format=duration",
                        "-of", "default=noprint_wrappers=1:nokey=1",
                        audio_file
                    ]

                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )

                    stdout, stderr = await process.communicate()

                    if process.returncode != 0:
                        print(f"获取音频时长失败: {stderr.decode()}")
                        audio_durations.append(0.0)
                        continue

                    audio_duration = float(stdout.decode().strip())
                    audio_durations.append(audio_duration)
                    print(f"批次 {i+1} 音频时长: {audio_duration:.3f}秒")

                except Exception as e:
                    print(f"获取音频时长出错: {str(e)}")
                    audio_durations.append(0.0)

            # 处理每个字幕文件
            for i, (srt_file, audio_duration) in enumerate(zip(srt_files, audio_durations)):
                print(f"\n处理批次 {i+1}/{len(srt_files)}")
                print(f"当前时间偏移: {cumulative_time_offset:.3f}秒")

                # 检查字幕文件是否存在
                if not os.path.exists(srt_file):
                    print(f"字幕文件不存在: {srt_file}")
                    # 即使字幕文件不存在，也要更新偏移
                    cumulative_time_offset += audio_duration
                    continue

                # 读取字幕文件
                try:
                    current_subs = pysrt.open(srt_file, encoding='utf-8')
                    print(f"读取到 {len(current_subs)} 条字幕")
                except Exception as e:
                    print(f"读取字幕文件失败: {str(e)}")
                    cumulative_time_offset += audio_duration
                    continue

                # 如果字幕文件为空，跳过
                if len(current_subs) == 0:
                    print(f"字幕文件为空")
                    cumulative_time_offset += audio_duration
                    continue

                # 简单处理：直接按SRT时间加上偏移
                for sub in current_subs:
                    new_sub = pysrt.SubRipItem()
                    new_sub.index = subtitle_index
                    subtitle_index += 1
                    new_sub.text = sub.text

                    # 获取SRT中的原始时间（秒）
                    original_start = self._subtitle_to_seconds(sub.start)
                    original_end = self._subtitle_to_seconds(sub.end)

                    # 简单加上累积偏移
                    new_start = cumulative_time_offset + original_start
                    new_end = cumulative_time_offset + original_end

                    # 设置新的时间戳
                    self._set_subtitle_time(new_sub.start, new_start)
                    self._set_subtitle_time(new_sub.end, new_end)

                    final_subs.append(new_sub)

                    print(f"  字幕 {subtitle_index-1}: {original_start:.2f}-{original_end:.2f} -> {new_start:.2f}-{new_end:.2f}")

                # 更新累积偏移（使用音频实际时长）
                cumulative_time_offset += audio_duration
                print(f"批次 {i+1} 完成，下一批次偏移: {cumulative_time_offset:.3f}秒")

            # 保存合并后的字幕文件
            final_subs.save(output_srt, encoding='utf-8')
            print(f"\n字幕合并完成！")
            print(f"总字幕数: {len(final_subs)} 条")
            print(f"总时长: {cumulative_time_offset:.2f}秒")

            # 合并完成，设置进度100%
            if progress_callback:
                progress_callback(100, "字幕合并完成")
            
        except Exception as e:
            print(f"合并字幕文件失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            raise e
    
    async def _process_batch(self, batch_index, batch_text, output_file, srt_file, semaphore, total_batches, progress_callback=None, batch_line_indices=None):
        """
        处理单个批次的文本
        只做音频合成和初步字幕生成，不做静音处理和复杂字幕同步。
        """
        # 添加到临时文件列表
        self.temp_files.append(output_file)
        # 使用信号量控制并发
        async with semaphore:
            try:
                # 检查是否被停止
                if self.stop_flag:
                    print(f"批次 {batch_index+1} 处理已取消")
                    if progress_callback:
                        progress_callback(0, "操作已取消")
                    return Exception("操作已取消")
                # 更新进度
                # 计算批次开始进度 (0-10%)
                start_progress = int((batch_index) / total_batches * 10)
                self._current_progress = start_progress
                if progress_callback:
                    progress_callback(self._current_progress, f"批次 {batch_index+1}/{total_batches} 开始 {self._current_progress}%")
                print(f"[批次处理] 开始处理批次 {batch_index+1}/{total_batches}")
                # 根据引擎选择处理方法
                engine = self.settings.get("engine", "edge-tts")
                # 仅保留 Edge TTS 引擎，其余自动回退
                if engine != "edge-tts":
                    engine = "edge-tts"
                    self.settings["engine"] = "edge-tts"
                # 设置重试参数
                max_retries = self.settings.get("retry_count", 3)
                retry_interval = self.settings.get("retry_interval", 2)
                current_retry = 0
                while True:
                    # 每次重试前检查是否已停止
                    if self.stop_flag:
                        print(f"批次 {batch_index+1} 重试已取消")
                        return Exception("操作已取消")
                    try:
                        if engine == "edge-tts":
                            # 生成音频和获取时间戳
                            timestamps = await self._edge_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            print(f"批次 {batch_index+1} 生成了 {len(timestamps)} 条字幕时间戳")
                            # 根据原始行索引调整字幕
                            timestamps = self._align_timestamps_with_original_text(timestamps, batch_line_indices)
                            # 生成字幕
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "azure-tts":
                            await self._azure_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            # 使用改进的时间戳生成方法
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "baidu-tts":
                            await self._baidu_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "xunfei-tts":
                            await self._xunfei_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "tencent-tts":
                            await self._tencent_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        else:
                            timestamps = await self._edge_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = self._align_timestamps_with_original_text(timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        # 处理成功，跳出循环
                        break
                    except Exception as e:
                        if self.stop_flag:
                            return Exception("操作已取消")
                        error_msg = str(e)
                        is_network_error = self._is_network_error(error_msg)
                        if is_network_error and current_retry < max_retries:
                            current_retry += 1
                            print(f"批次 {batch_index+1} 处理失败(网络错误): {error_msg}")
                            print(f"等待 {retry_interval} 秒后进行第 {current_retry}/{max_retries} 次重试...")
                            await asyncio.sleep(retry_interval)
                        else:
                            if is_network_error:
                                error_type = "网络错误"
                                error_detail = f"重试 {max_retries} 次后仍然失败: {error_msg}"
                            else:
                                error_type = "非网络错误"
                                error_detail = error_msg
                            print(f"批次 {batch_index+1} 处理失败({error_type}): {error_detail}")
                            raise Exception(f"处理批次 {batch_index+1} 失败({error_type}): {error_detail}")
                
                print(f"批次 {batch_index+1}/{total_batches} 处理完成")
                # 批次完成：递增计数并计算新百分比
                self._completed_batches += 1
                self._current_progress = int(10 + (self._completed_batches / total_batches) * 70)
                if progress_callback:
                    progress_callback(self._current_progress, f"批次 {self._completed_batches}/{total_batches} 完成 {self._current_progress}%")
                
                # 只保留批次级音频后处理：动态压缩/标准化/放大
                normalize_audio = self.settings.get("normalize_audio", False)
                amplify_db = float(self.settings.get("amplify_db", 0))
                
                # 先进行音频后处理（如果启用）
                if normalize_audio:
                    af_str = "acompressor=threshold=-20dB:ratio=6:attack=20:release=250,loudnorm"
                    if amplify_db != 0:
                        af_str += f",volume={amplify_db}dB"
                    print(f"批次 {batch_index+1} 动态压缩+标准化{'并放大' if amplify_db != 0 else ''}")
                    command = [
                        "ffmpeg", "-y",
                        "-i", output_file,
                        "-af", af_str,
                        "-b:a", "128k",
                        output_file + ".norm.mp3"
                    ]
                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        os.replace(output_file + ".norm.mp3", output_file)
                    else:
                        print(f"批次 {batch_index+1} 动态压缩/标准化失败")
                elif amplify_db != 0:
                    print(f"批次 {batch_index+1} 放大音量 {amplify_db} dB")
                    command = [
                        "ffmpeg", "-y",
                        "-i", output_file,
                        "-filter:a", f"volume={amplify_db}dB",
                        "-b:a", "128k",
                        output_file + ".amplified.mp3"
                    ]
                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        os.replace(output_file + ".amplified.mp3", output_file)
                    else:
                        print(f"批次 {batch_index+1} 音量放大失败")
                
            except Exception as e:
                if self.stop_flag:
                    return Exception("操作已取消")
                print(f"处理批次 {batch_index+1} 失败: {str(e)}")
                raise e
    
    async def _edge_tts_batch(self, text, output_file):
        """
        使用Edge TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
            
        返回:
            行级别的时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
        """
        try:
            # 从设置中获取参数
            voice = self.settings["voice"]
            # 修复计算逻辑，确保rate和volume使用正确的格式
            speed = self.settings['speed']
            rate = f"+{int((speed - 1) * 100)}%" if speed > 1 else f"-{int(abs((speed - 1) * 100))}%"
            if speed == 1:
                rate = "+0%"
            volume = f"+{int((self.settings['volume'] - 1) * 100)}%" if self.settings['volume'] > 1 else f"-{int(abs((self.settings['volume'] - 1) * 100))}%"
            if self.settings['volume'] == 1:
                volume = "+0%"
            
            # 修复pitch参数格式，使用Hz单位
            pitch_value = int((self.settings['pitch'] - 1) * 50)  # 将0.5-2.0的值映射到-25Hz到+50Hz
            pitch = f"+{pitch_value}Hz" if pitch_value >= 0 else f"{pitch_value}Hz"
            if self.settings['pitch'] == 1:
                pitch = "+0Hz"
            
            # 设置超时和重试参数
            timeout = 30  # 30秒超时
            connection_attempt = 0
            max_connection_attempts = 3
            
            # 用于存储字幕数据
            word_boundaries = []
            
            # 打开文件准备写入
            with open(output_file, "wb") as audio_file:
                # 添加连接重试逻辑
                while connection_attempt < max_connection_attempts:
                    try:
                        # 每次重试时创建新的通信对象，避免"stream can only be called once"错误
                        communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume, pitch=pitch)
                        
                        # 使用超时保护
                        audio_chunks = []
                        async for chunk in communicate.stream():
                            if chunk["type"] == "audio":
                                audio_data = chunk.get("data")
                                if audio_data is not None:
                                    audio_file.write(audio_data)
                                    # 记录已获取的数据块以备重试时使用
                                    audio_chunks.append(audio_data)
                            elif chunk["type"] == "WordBoundary":
                                word_boundaries.append(chunk)
                        # 如果成功完成，退出循环
                        break
                    except Exception as e:
                        connection_attempt += 1
                        error_msg = str(e)
                        
                        # 判断是否为网络错误
                        is_network_error = "timeout" in error_msg.lower() or "connection" in error_msg.lower() or "503" in error_msg
                        
                        if is_network_error and connection_attempt < max_connection_attempts:
                            print(f"Edge TTS连接错误(尝试 {connection_attempt}/{max_connection_attempts}): {error_msg}")
                            print(f"正在自动重新连接...")
                            # 短暂等待后重试
                            await asyncio.sleep(1)
                            # 如果已经有部分数据，保存已有数据
                            if audio_chunks:
                                print(f"已保存 {len(audio_chunks)} 个已下载的音频块")
                        else:
                            # 达到重试上限或非网络错误，抛出异常
                            raise
            
            # 处理字幕时间戳
            timestamps = []
            
            # 按句子分组的阈值（毫秒），随语速动态调整
            # 语速越慢，单词间停顿越长，需要更大的阈值；语速越快，则阈值应减小
            speed_factor = max(0.5, min(2.0, self.settings.get('speed', 1.0)))
            sentence_threshold = int(400 / speed_factor)  # 基准400ms，等比缩放
            
            # 分析收集到的边界信息
            if word_boundaries:
                current_text = []
                current_start = None
                current_end = None
                
                for i, boundary in enumerate(word_boundaries):
                    if "offset" in boundary and "duration" in boundary and "text" in boundary:
                        # 转换时间戳为毫秒
                        start_ms = int(boundary['offset'] / 10000)
                        duration_ms = int(boundary['duration'] / 10000)
                        end_ms = start_ms + duration_ms
                        word_text = boundary.get("text", "")
                        
                        # 如果是第一个词，开始新的句子
                        if current_start is None:
                            current_start = start_ms
                            current_text.append(word_text)
                            current_end = end_ms
                        else:
                            # 确保current_end不为None
                            if current_end is not None:
                                # 计算间隔
                                interval = start_ms - current_end
                                
                                # 如果间隔大于阈值或遇到句尾标点，结束当前句子
                                is_sentence_end = False
                                if i > 0 and "text" in word_boundaries[i-1] and word_boundaries[i-1]["text"]:
                                    is_sentence_end = word_boundaries[i-1]["text"][-1] in ",.!?;:，。！？；："
                                
                                if interval > sentence_threshold or is_sentence_end:
                                    # 保存当前句子
                                    sentence_text = "".join(current_text)
                                    # 转换为秒，确保current_start和current_end不为None
                                    if current_start is not None and current_end is not None:
                                        start_sec = current_start / 1000.0
                                        end_sec = current_end / 1000.0
                                        timestamps.append((sentence_text, start_sec, end_sec))
            
                                    # 开始新句子
                                    current_text = [word_text]
                                    current_start = start_ms
                                    current_end = end_ms
                                else:
                                    # 继续当前句子
                                    current_text.append(word_text)
                                    current_end = end_ms
                
                # 添加最后一句
                if current_text and current_start is not None and current_end is not None:
                    sentence_text = "".join(current_text)
                    # 转换为秒
                    start_sec = current_start / 1000.0
                    end_sec = current_end / 1000.0
                    timestamps.append((sentence_text, start_sec, end_sec))
            
            return timestamps
            
        except Exception as e:
            print(f"Edge TTS处理失败: {str(e)}")
            raise e    
    async def _generate_subtitles(self, timestamps, output_file):
        """
        生成SRT字幕文件
        
        参数:
            timestamps: 时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
            output_file: 输出文件路径
        """
        try:
            # 创建一个新的字幕文件
            subs = pysrt.SubRipFile()
            
            # 按顺序添加每条字幕
            for i, (line, start, end) in enumerate(timestamps):
                # 创建一个新的字幕项
                item = pysrt.SubRipItem()
                item.index = i + 1  # 字幕索引从1开始
                
                # 设置字幕内容
                item.text = line
                
                # 设置开始时间
                start_seconds = int(start)
                start_microseconds = int((start - start_seconds) * 1000000)
                item.start.hours = start_seconds // 3600
                item.start.minutes = (start_seconds % 3600) // 60
                item.start.seconds = start_seconds % 60
                item.start.milliseconds = start_microseconds // 1000
                
                # 设置结束时间
                end_seconds = int(end)
                end_microseconds = int((end - end_seconds) * 1000000)
                item.end.hours = end_seconds // 3600
                item.end.minutes = (end_seconds % 3600) // 60
                item.end.seconds = end_seconds % 60
                item.end.milliseconds = end_microseconds // 1000
                
                # 添加到字幕文件
                subs.append(item)
            
            # 保存字幕文件
            subs.save(output_file, encoding='utf-8')
            
            print(f"字幕文件已生成: {output_file}，共 {len(subs)} 条字幕")
            return True
            
        except Exception as e:
            print(f"生成字幕文件失败: {str(e)}")
            return False
    
    async def _merge_audio_files(self, output_file, audio_files):
        """
        合并音频文件
        参数:
            output_file: 输出文件路径
            audio_files: 要合并的音频文件列表
        """
        if not audio_files:
            return
        # 检查是否被停止
        if self.stop_flag:
            print("音频合并已取消")
            return
        # 过滤掉不存在的文件
        valid_audio_files = [f for f in audio_files if os.path.exists(f)]
        if not valid_audio_files:
            print("没有有效的音频文件可以合并")
            return
        temp_dir = os.path.join(os.path.dirname(output_file), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        # 检查是否被停止
        if self.stop_flag:
            print("音频合并已取消")
            return
        # 如果只有一个文件，直接处理
        if len(valid_audio_files) == 1:
            try:
                if self.stop_flag:
                    print("单文件处理已取消")
                    return
                shutil.copy2(valid_audio_files[0], output_file)
                print("单个文件处理完成")
            except Exception as e:
                print(f"音频处理出错: {str(e)}")
                if not self.stop_flag:
                    shutil.copy2(valid_audio_files[0], output_file)
            return
        # 多文件处理逻辑
        if self.stop_flag:
            print("多文件合并已取消")
            return
        file_list_path = os.path.join(temp_dir, "files.txt")
        base_name = os.path.splitext(os.path.basename(output_file))[0]
        renamed_files = []
        for i, audio_file in enumerate(valid_audio_files):
            if self.stop_flag:
                print("文件重命名过程已取消")
                return
            new_name = os.path.join(temp_dir, f"{base_name}-片段{i+1}.mp3")
            if audio_file != new_name:
                shutil.copy2(audio_file, new_name)
            renamed_files.append(new_name)
            self.temp_files.append(new_name)
        if self.stop_flag:
            print("文件列表创建已取消")
            return
        with open(file_list_path, "w", encoding="utf-8") as f:
            for file_path in renamed_files:
                f.write(f"file '{file_path.replace(os.sep, '/')} '\n")
        self.temp_files.append(file_list_path)
        temp_merged = os.path.join(temp_dir, "temp_merged.mp3")
        try:
            if self.stop_flag:
                print("FFmpeg合并过程已取消")
                return
            command = [
                "ffmpeg",
                "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", file_list_path,
                "-c", "copy",
                "-b:a", "128k",
                output_file
            ]
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self.startupinfo,
                creationflags=self.creation_flags
            )
            stdout, stderr = await process.communicate()
            if self.stop_flag:
                print("音频合并已取消")
                return
            if process.returncode != 0:
                error_message = f"文件合并失败，FFmpeg错误: {stderr.decode('utf-8', errors='replace')}"
                print(error_message)
                raise Exception(error_message)
            else:
                print("多个文件合并完成")
        except Exception as e:
            error_message = f"音频合并失败: {str(e)}"
            print(error_message)
            if not self.stop_flag:
                raise Exception(error_message)
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        print("开始清理临时文件...")
        
        # 创建要删除的文件列表的副本
        files_to_delete = self.temp_files.copy()
        self.temp_files = []
        
        # 记录删除成功和失败的文件数量
        deleted_count = 0
        failed_count = 0
        
        for file_path in files_to_delete:
            try:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except PermissionError:
                        print(f"无法删除文件(权限不足): {file_path}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
                    except Exception as e:
                        print(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
            except Exception as e:
                print(f"检查文件时出错: {file_path}, 错误: {str(e)}")
                failed_count += 1
        
        if deleted_count > 0:
            print(f"已删除 {deleted_count} 个临时文件" + (f"，{failed_count} 个文件删除失败" if failed_count > 0 else ""))
        
        # 尝试清理空的临时目录
        try:
            # 查找所有temp目录
            temp_dirs = set()
            
            # 从当前剩余的临时文件路径中提取目录
            for file_path in self.temp_files:
                dir_path = os.path.dirname(file_path)
                if os.path.basename(dir_path) == "temp" and os.path.exists(dir_path):
                    temp_dirs.add(dir_path)
            
            # 增加额外的检测 - 在已删除文件的路径中查找temp目录
            for file_path in files_to_delete:
                dir_path = os.path.dirname(file_path)
                if os.path.basename(dir_path) == "temp" and os.path.exists(dir_path):
                    temp_dirs.add(dir_path)
            
            # 统计删除的目录数
            deleted_dirs = 0
            
            # 遍历所有可能的temp目录，尝试删除空目录
            for temp_dir in temp_dirs:
                try:
                    if os.path.exists(temp_dir):
                        # 检查目录是否为空
                        if not os.listdir(temp_dir):
                            os.rmdir(temp_dir)
                            deleted_dirs += 1
                            print(f"已删除空的临时目录: {temp_dir}")
                        else:
                            # 目录非空，可能是其他进程正在使用，或者有无法删除的文件
                            if self.stop_flag:
                                # 如果是在停止操作中，尝试强制删除所有文件
                                try:
                                    for file_name in os.listdir(temp_dir):
                                        file_path = os.path.join(temp_dir, file_name)
                                        try:
                                            if os.path.isfile(file_path):
                                                os.remove(file_path)
                                            elif os.path.isdir(file_path):
                                                shutil.rmtree(file_path, ignore_errors=True)
                                        except Exception as e:
                                            print(f"强制清理时无法删除: {file_path}, 错误: {str(e)}")
                                    
                                    # 再次尝试删除目录
                                    if not os.listdir(temp_dir):
                                        os.rmdir(temp_dir)
                                        deleted_dirs += 1
                                        print(f"已删除清理后的临时目录: {temp_dir}")
                                except Exception as e:
                                    print(f"强制清理目录失败: {temp_dir}, 错误: {str(e)}")
                except Exception as e:
                    print(f"删除临时目录失败: {temp_dir}, 错误: {str(e)}")
            
            if deleted_dirs > 0:
                print(f"已删除 {deleted_dirs} 个临时目录")
                
        except Exception as e:
            print(f"清理临时目录过程中出错: {str(e)}")
        
        # 返回清理结果
        return {
            "deleted_files": deleted_count,
            "failed_files": failed_count
        }
    
    def stop(self):
        """停止当前处理"""
        print("TTS引擎: 正在执行停止操作...")
        self.stop_flag = True
        # 取消所有批量任务
        for task in getattr(self, '_batch_tasks', []):
            if not task.done():
                task.cancel()
        # 立即清理未完成的临时文件，避免等到对象销毁时才清理
        self._cleanup_temp_files()
        
    def __del__(self):
        """析构函数，清理资源"""
        # 清理临时文件
        self._cleanup_temp_files()
        
        # 关闭事件循环
        if self.loop and not self.loop.is_closed():
            self.loop.close()

    def _generate_basic_timestamps(self, text, timestamps):
        """
        为不提供详细时间戳的TTS引擎生成基本时间戳
        
        参数:
            text: 文本内容
            timestamps: 时间戳列表
        """
        # 按行分割文本
        lines = text.splitlines()
        total_chars = sum(len(line) for line in lines)
        
        # 假设每个字符平均时长为0.2秒
        char_duration = 0.2
        current_time = 0
        
        for line in lines:
            if not line.strip():
                continue
                
            # 计算当前行预计时长
            line_duration = len(line) * char_duration
            start_time = current_time
            end_time = start_time + line_duration
            
            # 添加时间戳
            timestamps.append((line, start_time, end_time))
            
            # 更新当前时间
            current_time = end_time

    async def _azure_tts_batch(self, text, output_file):
        """
        使用Azure TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._azure_tts_preview(text, output_file)

    async def _baidu_tts_batch(self, text, output_file):
        """
        使用百度TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._baidu_tts_preview(text, output_file)

    async def _xunfei_tts_batch(self, text, output_file):
        """
        使用讯飞TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._xunfei_tts_preview(text, output_file)

    async def _tencent_tts_batch(self, text, output_file):
        """
        使用腾讯TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._tencent_tts_preview(text, output_file)

    def _is_network_error(self, error_msg):
        """判断是否为网络错误"""
        network_keywords = ['connection', 'timeout', '503', 'network', 
                           '连接', '超时', '网络', '断开', 'refused', '拒绝']
        error_lower = error_msg.lower()
        return any(keyword in error_lower for keyword in network_keywords)

    async def _get_audio_duration(self, audio_file):
        """
        获取音频文件的时长（秒）
        
        参数:
            audio_file: 音频文件路径
            
        返回:
            音频时长（秒）
        """
        try:
            # 使用ffprobe获取音频时长
            command = [
                "ffprobe",
                "-v", "error",
                "-threads", str(self.settings.get("concurrent_tasks", 4)),
                "-thread_queue_size", "512",  # 增加线程队列大小
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                audio_file
            ]
            
            # 创建进程
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self.startupinfo,
                creationflags=self.creation_flags
            )
            
            # 等待进程完成
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                # 如果ffprobe失败，使用备选方法
                return self._estimate_audio_duration(audio_file)
            
            # 解析输出获取时长
            duration = float(stdout.decode('utf-8').strip())
            
            # 验证时长的有效性
            if duration <= 0:
                print(f"警告: ffprobe返回的音频时长为{duration}秒，使用估算方法")
                return self._estimate_audio_duration(audio_file)
                
            return duration
            
        except Exception as e:
            print(f"获取音频时长失败: {str(e)}")
            # 使用备选方法估算时长
            return self._estimate_audio_duration(audio_file)
    
    def _estimate_audio_duration(self, audio_file):
        """
        估算MP3文件时长（备选方法）
        
        参数:
            audio_file: 音频文件路径
            
        返回:
            估算的音频时长（秒）
        """
        try:
            # 获取文件大小（字节）
            file_size = os.path.getsize(audio_file)
            
            # MP3文件大致比特率（比特/秒）
            bitrate = 128000  # 假设128kbps的比特率
            
            # 估算时长：文件大小（位）/ 比特率
            estimated_duration = (file_size * 8) / bitrate
            
            # 添加一个小的安全余量
            estimated_duration += 0.1
            
            return estimated_duration
        except Exception as e:
            print(f"估算音频时长失败: {str(e)}")
            # 返回基于文件内最后一个字幕的时长
            try:
                srt_file = audio_file.replace('.mp3', '.srt')
                if os.path.exists(srt_file):
                    subs = pysrt.open(srt_file, encoding='utf-8')
                    if subs and len(subs) > 0:
                        last_sub = subs[-1]
                        duration = (last_sub.end.hours * 3600 + last_sub.end.minutes * 60 + 
                                last_sub.end.seconds + last_sub.end.milliseconds / 1000)
                        # 添加一个小的安全余量
                        return duration + 0.1
            except Exception as err:
                print(f"从字幕估算时长失败: {str(err)}")
            
            # 如果所有方法都失败，则返回一个默认值
            return 5.0  # 默认5秒 

    def _align_timestamps_with_original_text(self, timestamps, batch_line_indices):
        """
        根据原始行索引调整字幕时间戳，确保字幕内容与原文匹配
        
        参数:
            timestamps: 时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
            batch_line_indices: 批次中原始行索引列表
            
        返回:
            调整后的时间戳列表
        """
        if not batch_line_indices:
            return timestamps
            
        aligned_timestamps = []
        
        # 创建原始行内容到时间戳的映射
        # 使用文本相似度匹配，确保即使有细微差异也能匹配上
        text_to_timestamp = {}
        
        # 对于Edge TTS返回的时间戳，可能会有一些细微的差异
        # 例如标点符号可能会被省略或替换
        for text, start, end in timestamps:
            text_to_timestamp[text] = (start, end)
        
        # 遍历批次中的每个原始行索引
        for idx in batch_line_indices:
            if idx in self.original_text_mapping:
                original_line = self.original_text_mapping[idx]
                
                # 如果原始行为空，跳过
                if not original_line.strip():
                    continue
                
                # 尝试在时间戳中找到完全匹配的行
                if original_line in text_to_timestamp:
                    start, end = text_to_timestamp[original_line]
                    aligned_timestamps.append((original_line, start, end))
                else:
                    # 如果找不到完全匹配，尝试找到最相似的行
                    best_match = None
                    best_similarity = 0
                    
                    for gen_text in text_to_timestamp:
                        # 计算简单的相似度（共同字符数量 / 较长文本的长度）
                        common_chars = sum(1 for c in gen_text if c in original_line)
                        similarity = common_chars / max(len(gen_text), len(original_line))
                        
                        if similarity > best_similarity:
                            best_similarity = similarity
                            best_match = gen_text
                    
                    # 如果相似度大于0.7，认为找到了匹配
                    if best_match and best_similarity > 0.7:
                        start, end = text_to_timestamp[best_match]
                        aligned_timestamps.append((original_line, start, end))
                    else:
                        # 如果没有找到足够相似的匹配，使用估算的时间
                        # 根据行的长度和位置估算
                        if aligned_timestamps:
                            # 如果已经有之前的行，使用之前行的结束时间作为当前行的开始时间
                            prev_end = aligned_timestamps[-1][2]
                            # 估算当前行的持续时间（0.2秒每个字符）
                            duration = len(original_line) * 0.2
                            aligned_timestamps.append((original_line, prev_end, prev_end + duration))
                        else:
                            # 如果是第一行，从0开始
                            duration = len(original_line) * 0.2
                            aligned_timestamps.append((original_line, 0, duration))
        
        # 如果没有找到任何匹配，直接返回原始时间戳
        if not aligned_timestamps and timestamps:
            print("警告：未能将任何字幕与原文对齐，使用原始时间戳")
            return timestamps
            
        return aligned_timestamps

    def _generate_accurate_timestamps(self, text, timestamps, batch_line_indices):
        """
        为非Edge TTS引擎生成更准确的时间戳
        
        参数:
            text: 文本内容
            timestamps: 时间戳列表（将被填充）
            batch_line_indices: 批次中原始行索引列表
        """
        # 这个方法用于为其他TTS引擎生成更准确的时间戳
        
        # 首先尝试使用FFmpeg分析音频文件中的静音部分来确定段落边界
        # 如果没有音频文件或分析失败，则使用改进的估算方法
        
        # 按行分割文本
        lines = text.splitlines()
        
        # 过滤空行
        non_empty_lines = [(i, line) for i, line in enumerate(lines) if line.strip()]
        
        if not non_empty_lines:
            return
            
        # 计算总字符数
        total_chars = sum(len(line) for _, line in non_empty_lines)
        
        # 根据字符数估算每个字符的平均时长
        # 假设每个汉字约需0.3秒，每个字母约需0.1秒
        def estimate_duration(text):
            # 计算汉字数量
            chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
            # 计算字母和数字数量
            alphanumeric = sum(1 for c in text if c.isalnum() and not '\u4e00' <= c <= '\u9fff')
            # 估算总时长
            return chinese_chars * 0.3 + alphanumeric * 0.1 + 0.2  # 额外0.2秒作为句子间隔
        
        # 估算总时长
        total_duration = sum(estimate_duration(line) for _, line in non_empty_lines)
        
        # 设置当前时间点
        current_time = 0
        
        # 为每行生成时间戳
        for i, (orig_idx, line) in enumerate(non_empty_lines):
            if not line.strip():
                continue
                
            # 估算当前行的时长
            line_duration = estimate_duration(line)
            
            # 设置开始和结束时间
            start_time = current_time
            end_time = start_time + line_duration
            
            # 添加到时间戳列表
            timestamps.append((line, start_time, end_time))
            
            # 更新当前时间
            current_time = end_time
            
            # 如果不是最后一行，添加小的间隔
            if i < len(non_empty_lines) - 1:
                current_time += 0.1

    def _post_process_subtitles(self, final_subs):
        """
        字幕后处理，修复重叠问题和确保字幕不会太短

        参数:
            final_subs: 字幕列表
        """
        print("执行字幕后处理，修复可能的重叠问题...")
        for i in range(1, len(final_subs)):
            prev_sub = final_subs[i-1]
            curr_sub = final_subs[i]

            # 获取前一个字幕的结束时间和当前字幕的开始时间（秒）
            prev_end_sec = self._subtitle_to_seconds(prev_sub.end)
            curr_start_sec = self._subtitle_to_seconds(curr_sub.start)

            # 如果存在重叠，调整前一个字幕的结束时间
            if prev_end_sec > curr_start_sec:
                # 设置一个小的间隔（0.05秒）
                gap = 0.05
                new_prev_end = curr_start_sec - gap

                # 确保字幕不会太短（至少0.3秒）
                prev_start_sec = self._subtitle_to_seconds(prev_sub.start)
                if new_prev_end - prev_start_sec < 0.3:
                    # 如果调整后前一个字幕太短，则调整当前字幕的开始时间
                    new_curr_start = prev_start_sec + 0.3 + gap

                    # 更新当前字幕的开始时间
                    self._set_subtitle_time(curr_sub.start, new_curr_start)
                else:
                    # 更新前一个字幕的结束时间
                    self._set_subtitle_time(prev_sub.end, new_prev_end)

    def _validate_subtitle_audio_sync(self, final_subs, cumulative_offset, output_srt):
        """
        验证字幕与音频的时长一致性

        参数:
            final_subs: 字幕列表
            cumulative_offset: 累积音频时长
            output_srt: 输出字幕文件路径
        """
        # 取字幕最后一条结束时间作为字幕总时长
        if len(final_subs) > 0:
            last_sub = final_subs[-1]
            subs_total_sec = self._subtitle_to_seconds(last_sub.end)
        else:
            subs_total_sec = 0

        audio_total_sec = cumulative_offset  # 已累积的音频总时长
        diff_sec = abs(audio_total_sec - subs_total_sec)

        if diff_sec > 60:  # 超过1分钟误差
            try:
                import tkinter as _tk
                from tkinter import messagebox as _mb
                _mb.showwarning(
                    "字幕与音频时长不一致",
                    f"文件 {os.path.basename(output_srt)}\n字幕总时长与音频时长相差 {diff_sec:.1f} 秒，建议重新配音。"
                )
            except Exception:
                # 如果在无GUI环境下，退化为打印警告
                print(f"警告: 字幕与音频时长相差 {diff_sec:.1f} 秒 (>60s)，请检查 {output_srt}")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        print("开始清理临时文件...")

        # 创建要删除的文件列表的副本
        files_to_delete = self.temp_files.copy()
        self.temp_files = []

        # 记录删除成功和失败的文件数量
        deleted_count = 0
        failed_count = 0

        for file_path in files_to_delete:
            try:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except PermissionError:
                        print(f"无法删除文件(权限不足): {file_path}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
                    except Exception as e:
                        print(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
            except Exception as e:
                print(f"检查文件时出错: {file_path}, 错误: {str(e)}")
                failed_count += 1

        if deleted_count > 0:
            print(f"已删除 {deleted_count} 个临时文件" + (f"，{failed_count} 个文件删除失败" if failed_count > 0 else ""))

        print("临时文件清理完成")

    def stop_generation(self):
        """停止音频生成"""
        print("正在停止音频生成...")
        self.stop_flag = True

        # 取消所有正在运行的批量任务
        if hasattr(self, '_batch_tasks') and self._batch_tasks:
            print(f"正在取消 {len(self._batch_tasks)} 个批量任务...")
            for task in self._batch_tasks:
                if not task.done():
                    task.cancel()
            print("批量任务取消完成")

        print("音频生成停止完成")
