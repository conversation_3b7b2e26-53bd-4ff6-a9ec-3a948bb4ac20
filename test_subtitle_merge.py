#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字幕合并功能的脚本
"""

import os
import sys
import asyncio
import tempfile
import shutil

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'TTS', 'src'))

from core.tts_engine import TTSEngine

def create_test_files():
    """创建测试用的音频和字幕文件"""
    temp_dir = tempfile.mkdtemp()
    print(f"创建测试目录: {temp_dir}")
    
    # 创建测试音频文件（空文件，只用于测试时长获取）
    audio_files = []
    srt_files = []
    
    # 第一个批次
    audio1 = os.path.join(temp_dir, "batch_1.mp3")
    srt1 = os.path.join(temp_dir, "batch_1.srt")
    
    # 创建空的音频文件（实际测试中需要真实的音频文件）
    with open(audio1, 'wb') as f:
        f.write(b'')  # 空文件
    
    # 创建第一个字幕文件
    srt1_content = """1
00:00:00,000 --> 00:00:02,500
这是第一段文本

2
00:00:02,500 --> 00:00:05,000
这是第二段文本
"""
    with open(srt1, 'w', encoding='utf-8') as f:
        f.write(srt1_content)
    
    audio_files.append(audio1)
    srt_files.append(srt1)
    
    # 第二个批次
    audio2 = os.path.join(temp_dir, "batch_2.mp3")
    srt2 = os.path.join(temp_dir, "batch_2.srt")
    
    with open(audio2, 'wb') as f:
        f.write(b'')  # 空文件
    
    # 创建第二个字幕文件
    srt2_content = """1
00:00:00,000 --> 00:00:03,000
这是第三段文本

2
00:00:03,000 --> 00:00:06,000
这是第四段文本
"""
    with open(srt2, 'w', encoding='utf-8') as f:
        f.write(srt2_content)
    
    audio_files.append(audio2)
    srt_files.append(srt2)
    
    return temp_dir, audio_files, srt_files

async def test_subtitle_merge():
    """测试字幕合并功能"""
    print("开始测试字幕合并功能...")
    
    # 创建测试文件
    temp_dir, audio_files, srt_files = create_test_files()
    
    try:
        # 创建TTS引擎实例
        settings = {
            "voice": "zh-CN-XiaoxiaoNeural",
            "speed": 1.0,
            "pitch": 1.0,
            "volume": 1.0,
            "batch_size": 5000,
            "engine": "edge-tts"
        }
        
        tts_engine = TTSEngine(settings)
        
        # 输出文件路径
        output_srt = os.path.join(temp_dir, "merged.srt")
        
        print(f"输入文件:")
        for i, (audio, srt) in enumerate(zip(audio_files, srt_files)):
            print(f"  批次 {i+1}: {audio} + {srt}")
        
        print(f"输出文件: {output_srt}")
        
        # 由于我们没有真实的音频文件，我们需要模拟音频时长
        # 在实际使用中，这些时长会通过FFprobe自动获取
        print("\n注意：由于测试环境限制，音频时长获取可能失败")
        print("这是正常的，重点是测试字幕时间戳调整逻辑")
        
        # 执行字幕合并
        await tts_engine._merge_subtitle_files(output_srt, srt_files, audio_files)
        
        # 检查输出文件
        if os.path.exists(output_srt):
            print(f"\n✅ 字幕合并成功！输出文件: {output_srt}")
            
            # 读取并显示合并后的字幕内容
            with open(output_srt, 'r', encoding='utf-8') as f:
                content = f.read()
                print("\n合并后的字幕内容:")
                print("=" * 50)
                print(content)
                print("=" * 50)
        else:
            print("❌ 字幕合并失败，输出文件不存在")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 已清理测试目录: {temp_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {str(e)}")

def main():
    """主函数"""
    print("字幕合并功能测试")
    print("=" * 50)
    
    # 运行异步测试
    asyncio.run(test_subtitle_merge())
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
